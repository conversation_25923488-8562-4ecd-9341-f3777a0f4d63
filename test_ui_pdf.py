#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار واجهة تصدير PDF
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QMessageBox
from PyQt5.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.pdf_exporter import PDFExporter

class TestPDFWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.pdf_exporter = PDFExporter()
        self.setup_ui()
        
        # بيانات تجريبية
        self.test_data = [
            {
                'id': 1,
                'checkpoint_name': 'نقطة تفتيش الشمال',
                'plate_number': 'أ ب ج 123',
                'person_name': 'أحمد محمد',
                'identity_number': '1234567890',
                'direction': 'دخول',
                'movement_time': datetime.now().isoformat(),
                'notes': 'ملاحظات تجريبية'
            },
            {
                'id': 2,
                'checkpoint_name': 'نقطة تفتيش الجنوب',
                'plate_number': 'د هـ و 456',
                'person_name': 'فاطمة علي',
                'identity_number': '0987654321',
                'direction': 'خروج',
                'movement_time': datetime.now().isoformat(),
                'notes': 'ملاحظات أخرى'
            },
            {
                'id': 3,
                'checkpoint_name': 'نقطة تفتيش الشرق',
                'plate_number': 'ز ح ط 789',
                'person_name': 'محمد عبدالله',
                'identity_number': '1122334455',
                'direction': 'دخول',
                'movement_time': datetime.now().isoformat(),
                'notes': 'بدون ملاحظات'
            }
        ]
    
    def setup_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار تصدير PDF")
        self.setGeometry(100, 100, 400, 200)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout(self)
        
        # زر تصدير PDF
        self.export_pdf_button = QPushButton("تصدير إلى PDF")
        self.export_pdf_button.clicked.connect(self.export_to_pdf)
        self.export_pdf_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        
        layout.addWidget(self.export_pdf_button)
    
    def export_to_pdf(self):
        """تصدير البيانات إلى PDF"""
        try:
            if not self.test_data:
                QMessageBox.information(self, "تنبيه", "لا توجد بيانات للتصدير")
                return
            
            # تصدير البيانات إلى PDF
            filepath = self.pdf_exporter.export_movements_to_pdf(self.test_data)
            
            # فتح مجلد التقارير
            if os.path.exists(filepath):
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير بنجاح:\n{filepath}")
                # فتح مجلد التقارير في مستكشف الملفات
                os.startfile(os.path.dirname(filepath))
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إنشاء ملف التقرير")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات إلى PDF: {str(e)}")

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    widget = TestPDFWidget()
    widget.show()
    
    return app.exec_()

if __name__ == "__main__":
    main()
