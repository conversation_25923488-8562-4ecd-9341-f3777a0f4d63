#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لوظيفة تصدير PDF في التطبيق الكامل
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtTest import QTest

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pdf_export_integration():
    """اختبار تكامل تصدير PDF مع التطبيق الكامل"""
    try:
        print("🚀 بدء اختبار تكامل PDF مع التطبيق الكامل...")
        
        # اختبار استيراد جميع المكونات
        print("📦 اختبار استيراد المكونات...")
        
        from utils.pdf_exporter import PDFExporter
        print("✅ تم استيراد PDFExporter بنجاح")
        
        from ui.movements_view_widget import MovementsViewWidget
        print("✅ تم استيراد MovementsViewWidget بنجاح")
        
        from models.movement_model import MovementModel
        print("✅ تم استيراد MovementModel بنجاح")
        
        from database import Database
        print("✅ تم استيراد Database بنجاح")
        
        # اختبار قاعدة البيانات
        print("\n🗄️ اختبار قاعدة البيانات...")
        db = Database()
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # اختبار تسجيل الدخول
        print("\n🔐 اختبار تسجيل الدخول...")
        user = db.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ تم تسجيل الدخول بنجاح: {user['username']} ({user['role']})")
        else:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        # إنشاء تطبيق Qt
        print("\n🖥️ إنشاء تطبيق Qt...")
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء widget عرض الحركات
        print("📊 إنشاء widget عرض الحركات...")
        movements_widget = MovementsViewWidget(user_data=user)
        print("✅ تم إنشاء MovementsViewWidget بنجاح")
        
        # التحقق من وجود زر PDF
        if hasattr(movements_widget, 'export_pdf_button'):
            print("✅ زر تصدير PDF موجود")
        else:
            print("❌ زر تصدير PDF غير موجود")
            return False
        
        # التحقق من وجود مُصدِّر PDF
        if hasattr(movements_widget, 'pdf_exporter'):
            print("✅ مُصدِّر PDF موجود")
            print(f"   الخط المستخدم: {movements_widget.pdf_exporter.arabic_font_name}")
        else:
            print("❌ مُصدِّر PDF غير موجود")
            return False
        
        # اختبار تحميل البيانات
        print("\n📋 اختبار تحميل البيانات...")
        movements_widget.load_movements()
        data_count = len(movements_widget.current_data)
        print(f"✅ تم تحميل {data_count} حركة")
        
        # اختبار تصدير PDF
        print("\n📄 اختبار تصدير PDF...")
        if data_count > 0:
            try:
                # محاولة تصدير PDF
                filepath = movements_widget.pdf_exporter.export_movements_to_pdf(movements_widget.current_data)
                
                if os.path.exists(filepath):
                    file_size = os.path.getsize(filepath)
                    print(f"✅ تم إنشاء ملف PDF بنجاح: {filepath}")
                    print(f"   حجم الملف: {file_size} بايت")
                    
                    # التحقق من محتوى الملف
                    if file_size > 1000:  # ملف PDF صالح يجب أن يكون أكبر من 1KB
                        print("✅ حجم الملف مناسب - يبدو أن الملف صالح")
                    else:
                        print("⚠️ حجم الملف صغير - قد يكون هناك مشكلة")
                    
                    return True
                else:
                    print("❌ فشل في إنشاء ملف PDF")
                    return False
                    
            except Exception as e:
                print(f"❌ خطأ في تصدير PDF: {e}")
                return False
        else:
            print("⚠️ لا توجد بيانات للاختبار - سيتم إنشاء بيانات تجريبية")
            
            # إنشاء بيانات تجريبية
            test_data = [
                {
                    'id': 999,
                    'checkpoint_name': 'نقطة اختبار',
                    'plate_number': 'اختبار 123',
                    'person_name': 'مستخدم تجريبي',
                    'identity_number': '1234567890',
                    'direction': 'دخول',
                    'movement_time': datetime.now().isoformat(),
                    'notes': 'بيانات اختبار'
                }
            ]
            
            try:
                filepath = movements_widget.pdf_exporter.export_movements_to_pdf(test_data)
                if os.path.exists(filepath):
                    file_size = os.path.getsize(filepath)
                    print(f"✅ تم إنشاء ملف PDF تجريبي بنجاح: {filepath}")
                    print(f"   حجم الملف: {file_size} بايت")
                    return True
                else:
                    print("❌ فشل في إنشاء ملف PDF التجريبي")
                    return False
            except Exception as e:
                print(f"❌ خطأ في تصدير PDF التجريبي: {e}")
                return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لوظيفة تصدير PDF")
    print("=" * 60)
    
    success = test_pdf_export_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 جميع الاختبارات نجحت! وظيفة تصدير PDF تعمل بشكل صحيح")
        print("✅ التطبيق جاهز للاستخدام")
    else:
        print("❌ فشل في بعض الاختبارات")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
