#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مُصدِّر PDF للتقارير مع دعم اللغة العربية
"""

import os
from datetime import datetime
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.pdfbase.pdfmetrics import registerFont
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError as e:
    print(f"تحذير: لم يتم العثور على مكتبة reportlab: {e}")
    REPORTLAB_AVAILABLE = False
from PyQt5.QtWidgets import QMessageBox, QFileDialog


class PDFExporter:
    def __init__(self):
        if not REPORTLAB_AVAILABLE:
            raise ImportError("مكتبة reportlab غير متاحة. يرجى تثبيتها باستخدام: pip install reportlab")
        self.arabic_font_name = None
        self.setup_arabic_font()
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # قائمة بالخطوط العربية المحتملة في النظام
            font_paths = [
                r"C:\Windows\Fonts\arial.ttf",  # Arial يدعم العربية جزئياً
                r"C:\Windows\Fonts\tahoma.ttf",  # Tahoma يدعم العربية
                r"C:\Windows\Fonts\calibri.ttf",  # Calibri يدعم العربية
                r"C:\Windows\Fonts\DejaVuSans.ttf",  # DejaVu Sans
                r"C:\Windows\Fonts\DejaVuSans-Bold.ttf",
                r"E:\project\pythonP\fonts\DejaVuSans.ttf",
            ]
            
            # البحث عن خط متاح
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font_name = os.path.basename(font_path).replace('.ttf', '')
                        registerFont(TTFont(font_name, font_path))
                        self.arabic_font_name = font_name
                        print(f"تم تسجيل الخط: {font_name}")
                        break
                    except Exception as e:
                        print(f"فشل في تسجيل الخط {font_path}: {e}")
                        continue
            
            # إذا لم يتم العثور على خط، استخدم الخط الافتراضي
            if not self.arabic_font_name:
                self.arabic_font_name = "Helvetica"
                print("تحذير: لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
                
        except Exception as e:
            print(f"خطأ في إعداد الخط العربي: {e}")
            self.arabic_font_name = "Helvetica"
    
    def export_movements_to_pdf(self, movements_data, save_path=None):
        """تصدير الحركات إلى ملف PDF"""
        try:
            # إنشاء اسم الملف
            current_date = datetime.now().strftime("%Y-%m-%d")
            filename = f"تقرير_اليوم_{current_date}.pdf"
            
            # تحديد مسار الحفظ
            if save_path is None:
                # إنشاء مجلد التقارير إذا لم يكن موجوداً
                reports_dir = "reports"
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)
                save_path = os.path.join(reports_dir, filename)
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(
                save_path,
                pagesize=A4,
                rightMargin=0.5*inch,
                leftMargin=0.5*inch,
                topMargin=0.5*inch,
                bottomMargin=0.5*inch
            )
            
            # قائمة العناصر للمستند
            story = []
            
            # إضافة العنوان
            self.add_title(story)
            
            # إضافة التاريخ
            self.add_date(story)
            
            # إضافة مساحة
            story.append(Spacer(1, 0.2*inch))
            
            # إضافة جدول البيانات
            self.add_data_table(story, movements_data)
            
            # بناء المستند
            doc.build(story)
            
            return save_path
            
        except Exception as e:
            raise Exception(f"فشل في إنشاء ملف PDF: {str(e)}")
    
    def add_title(self, story):
        """إضافة العنوان للمستند"""
        try:
            # إنشاء نمط العنوان
            title_style = ParagraphStyle(
                'ArabicTitle',
                fontName=self.arabic_font_name,
                fontSize=18,
                alignment=1,  # وسط
                spaceAfter=0.2*inch,
                textColor=colors.darkblue
            )
            
            title = Paragraph("تقرير الحركات اليومية", title_style)
            story.append(title)
            
        except Exception as e:
            print(f"خطأ في إضافة العنوان: {e}")
    
    def add_date(self, story):
        """إضافة التاريخ للمستند"""
        try:
            # إنشاء نمط التاريخ
            date_style = ParagraphStyle(
                'ArabicDate',
                fontName=self.arabic_font_name,
                fontSize=12,
                alignment=1,  # وسط
                spaceAfter=0.1*inch,
                textColor=colors.black
            )
            
            current_date = datetime.now().strftime("%Y-%m-%d")
            date_text = f"تاريخ التقرير: {current_date}"
            date_para = Paragraph(date_text, date_style)
            story.append(date_para)
            
        except Exception as e:
            print(f"خطأ في إضافة التاريخ: {e}")
    
    def add_data_table(self, story, movements_data):
        """إضافة جدول البيانات للمستند"""
        try:
            # إعداد البيانات للجدول
            table_data = []
            
            # إضافة رؤوس الأعمدة (بترتيب RTL)
            headers = ["الاتجاه", "الوقت", "نقطة التفتيش", "السائق", "اللوحة"]
            table_data.append(headers)
            
            # إضافة بيانات الحركات
            for movement in movements_data:
                # تنسيق الوقت
                movement_time = movement.get('movement_time', '')
                if movement_time:
                    try:
                        if isinstance(movement_time, str):
                            dt = datetime.fromisoformat(movement_time.replace('Z', '+00:00'))
                        else:
                            dt = movement_time
                        formatted_time = dt.strftime("%H:%M")
                    except:
                        formatted_time = str(movement_time)
                else:
                    formatted_time = ''
                
                # إضافة صف البيانات (بترتيب RTL)
                row = [
                    movement.get('direction', '') or '',
                    formatted_time,
                    movement.get('checkpoint_name', '') or '',
                    movement.get('person_name', '') or '',
                    movement.get('plate_number', '') or ''
                ]
                table_data.append(row)
            
            # إنشاء الجدول
            table = Table(table_data, colWidths=[1*inch, 1.2*inch, 1.5*inch, 1.5*inch, 1.2*inch])
            
            # تطبيق نمط الجدول
            table.setStyle(TableStyle([
                # نمط الرؤوس
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                
                # حدود الجدول
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),
                
                # تلوين الصفوف المتناوبة
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
                
                # حشو الخلايا
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            
            story.append(table)
            
        except Exception as e:
            print(f"خطأ في إضافة جدول البيانات: {e}")
            # إضافة رسالة خطأ للمستند
            error_style = ParagraphStyle(
                'Error',
                fontName=self.arabic_font_name,
                fontSize=12,
                alignment=1,
                textColor=colors.red
            )
            error_para = Paragraph(f"خطأ في تحميل البيانات: {str(e)}", error_style)
            story.append(error_para)
    
    def get_save_location(self):
        """الحصول على موقع الحفظ من المستخدم"""
        try:
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_اليوم_{current_date}.pdf"
            
            file_path, _ = QFileDialog.getSaveFileName(
                None,
                "حفظ تقرير PDF",
                default_filename,
                "PDF Files (*.pdf);;All Files (*)"
            )
            
            return file_path if file_path else None
            
        except Exception as e:
            print(f"خطأ في اختيار موقع الحفظ: {e}")
            return None
