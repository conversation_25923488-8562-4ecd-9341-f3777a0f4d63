#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تصدير PDF
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.pdf_exporter import PDFExporter
    
    # بيانات تجريبية للاختبار
    test_data = [
        {
            'id': 1,
            'checkpoint_name': 'نقطة تفتيش الشمال',
            'plate_number': 'أ ب ج 123',
            'person_name': 'أحمد محمد',
            'identity_number': '1234567890',
            'direction': 'دخول',
            'movement_time': datetime.now().isoformat(),
            'notes': 'ملاحظات تجريبية'
        },
        {
            'id': 2,
            'checkpoint_name': 'نقطة تفتيش الجنوب',
            'plate_number': 'د هـ و 456',
            'person_name': 'فاطمة علي',
            'identity_number': '0987654321',
            'direction': 'خروج',
            'movement_time': datetime.now().isoformat(),
            'notes': 'ملاحظات أخرى'
        }
    ]
    
    print("بدء اختبار تصدير PDF...")
    
    # إنشاء مُصدِّر PDF
    pdf_exporter = PDFExporter()
    print(f"تم إنشاء مُصدِّر PDF بنجاح. الخط المستخدم: {pdf_exporter.arabic_font_name}")
    
    # تصدير البيانات
    filepath = pdf_exporter.export_movements_to_pdf(test_data)
    print(f"تم إنشاء ملف PDF بنجاح: {filepath}")
    
    # التحقق من وجود الملف
    if os.path.exists(filepath):
        print("✓ تم إنشاء الملف بنجاح")
        print(f"حجم الملف: {os.path.getsize(filepath)} بايت")
        
        # فتح مجلد التقارير
        os.startfile(os.path.dirname(filepath))
    else:
        print("✗ فشل في إنشاء الملف")
        
except Exception as e:
    print(f"خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
