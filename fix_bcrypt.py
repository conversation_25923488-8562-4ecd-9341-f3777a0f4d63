#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشكلة bcrypt
"""

import subprocess
import sys

def fix_bcrypt():
    """إصلاح مشكلة bcrypt"""
    try:
        print("بدء إصلاح مشكلة bcrypt...")
        
        # إلغاء تثبيت bcrypt الحالي
        print("إلغاء تثبيت bcrypt الحالي...")
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "bcrypt", "-y"], check=True)
        
        # إلغاء تثبيت passlib
        print("إلغاء تثبيت passlib...")
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "passlib", "-y"], check=True)
        
        # تثبيت bcrypt الجديد
        print("تثبيت bcrypt...")
        subprocess.run([sys.executable, "-m", "pip", "install", "bcrypt"], check=True)
        
        # تثبيت passlib
        print("تثبيت passlib...")
        subprocess.run([sys.executable, "-m", "pip", "install", "passlib"], check=True)
        
        print("✅ تم إصلاح مشكلة bcrypt بنجاح")
        
        # اختبار bcrypt
        print("اختبار bcrypt...")
        from passlib.hash import bcrypt
        test_hash = bcrypt.hash("test")
        if bcrypt.verify("test", test_hash):
            print("✅ bcrypt يعمل بشكل صحيح")
        else:
            print("❌ bcrypt لا يزال لا يعمل")
            
    except Exception as e:
        print(f"❌ فشل في إصلاح bcrypt: {e}")

if __name__ == "__main__":
    fix_bcrypt()
